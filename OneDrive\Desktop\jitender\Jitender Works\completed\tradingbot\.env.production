# Production Environment Configuration
# Copy this file to .env and fill in your actual values

# Angel One API credentials
ANGEL_API_KEY=your_angel_api_key_here
ANGEL_CLIENT_ID=your_angel_client_id_here
ANGEL_PASSWORD=your_angel_password_here
ANGEL_TOKEN=your_angel_token_here

# Five Paisa API credentials (optional)
FIVE_PAISA_EMAIL=your_five_paisa_email
FIVE_PAISA_PASSWORD=your_five_paisa_password
FIVE_PAISA_DOB=your_dob_ddmmyyyy

# Cashfree Payment Gateway
CASHFREE_CLIENT_ID=your_cashfree_client_id
CASHFREE_CLIENT_SECRET=your_cashfree_client_secret
CASHFREE_ENV=PROD  # PROD for production, TEST for testing

# Database configuration
DATABASE_URL=sqlite:///trading_bot.db

# Web interface
WEB_PORT=5000
WEB_HOST=0.0.0.0
WEB_SECRET_KEY=your_secret_key_for_sessions

# Telegram notifications (optional)
TELEGRAM_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/

# Trading Configuration
TRADING_MODE=LIVE  # DEMO or LIVE
MAX_DAILY_TRADES=10
DAILY_LOSS_LIMIT=2000
RISK_PER_TRADE=1.0

# Fund Management
MIN_BALANCE_THRESHOLD=10000
AUTO_FUND_AMOUNT=25000
MAX_DAILY_FUNDING=50000
AUTO_WITHDRAW_THRESHOLD=100000
AUTO_WITHDRAW_AMOUNT=50000
MIN_TRADING_BALANCE=15000

# Bank Account Details (for auto-funding)
BANK_ACCOUNT_NUMBER=your_bank_account_number
BANK_IFSC_CODE=your_bank_ifsc_code
BANK_ACCOUNT_NAME=your_account_name

# Security
ADMIN_ACCESS=false  # Set to true for admin bypass
LICENSE_SERVER_URL=https://your-license-server.com/api
