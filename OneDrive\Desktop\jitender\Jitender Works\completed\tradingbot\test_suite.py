#!/usr/bin/env python3
"""
Comprehensive test suite for Trading Bot
Run this before deploying to production
"""

import unittest
import sys
import os
import tempfile
import sqlite3
from unittest.mock import Mock, patch, MagicMock

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading_bot import TradingBot
from risk_manager import RiskManager
from strategies import TradingStrategy
from database import init_db, record_trade, get_open_trades
from performance_metrics import PerformanceMetrics
from license_manager import LicenseManager

class TestRiskManager(unittest.TestCase):
    def setUp(self):
        self.risk_manager = RiskManager(
            max_trades=5,
            daily_loss_limit=1000,
            max_drawdown=5
        )
        
    def test_can_trade_basic(self):
        """Test basic trading permission"""
        can_trade, reason = self.risk_manager.can_trade(10000, [])
        self.assertTrue(can_trade)
        
    def test_max_trades_limit(self):
        """Test maximum trades limit"""
        self.risk_manager.daily_trades = 5
        can_trade, reason = self.risk_manager.can_trade(10000, [])
        self.assertFalse(can_trade)
        self.assertIn("Maximum daily trades", reason)
        
    def test_daily_loss_limit(self):
        """Test daily loss limit"""
        self.risk_manager.daily_loss = 1000
        can_trade, reason = self.risk_manager.can_trade(10000, [])
        self.assertFalse(can_trade)
        self.assertIn("Daily loss limit", reason)
        
    def test_position_size_calculation(self):
        """Test position size calculation"""
        size = self.risk_manager.calculate_position_size(10000, 2.0, 1.0)
        expected_size = 10000 * 0.02 / 0.01  # 2% risk, 1% stop loss
        self.assertEqual(size, expected_size)

class TestTradingStrategy(unittest.TestCase):
    def setUp(self):
        self.strategy = TradingStrategy()
        
    @patch('strategies.NSEDataFetcher')
    def test_signal_generation(self, mock_fetcher):
        """Test signal generation"""
        # Mock data
        mock_data = {
            'close': [100, 101, 102, 103, 104],
            'volume': [1000, 1100, 1200, 1300, 1400],
            'high': [101, 102, 103, 104, 105],
            'low': [99, 100, 101, 102, 103]
        }
        
        mock_fetcher.return_value.get_historical_data.return_value = mock_data
        
        signals = self.strategy.calculate_signals(mock_data)
        self.assertIn('signal', signals)
        self.assertIn('strength', signals)

class TestDatabase(unittest.TestCase):
    def setUp(self):
        # Create temporary database
        self.db_fd, self.db_path = tempfile.mkstemp()
        os.environ['DATABASE_URL'] = f'sqlite:///{self.db_path}'
        init_db()
        
    def tearDown(self):
        os.close(self.db_fd)
        os.unlink(self.db_path)
        
    def test_record_trade(self):
        """Test trade recording"""
        trade_id = record_trade("RELIANCE", "buy", 2500.0, 10)
        self.assertGreater(trade_id, 0)
        
    def test_get_open_trades(self):
        """Test getting open trades"""
        record_trade("RELIANCE", "buy", 2500.0, 10)
        trades = get_open_trades()
        self.assertEqual(len(trades), 1)
        self.assertEqual(trades[0]['symbol'], "RELIANCE")

class TestPerformanceMetrics(unittest.TestCase):
    def setUp(self):
        self.metrics = PerformanceMetrics()
        
    def test_sharpe_ratio_calculation(self):
        """Test Sharpe ratio calculation"""
        returns = [0.01, 0.02, -0.01, 0.015, 0.005]
        sharpe = self.metrics.calculate_sharpe_ratio(returns)
        self.assertIsInstance(sharpe, float)
        
    def test_max_drawdown_calculation(self):
        """Test maximum drawdown calculation"""
        portfolio_values = [10000, 10500, 10200, 9800, 10100, 10300]
        result = self.metrics.calculate_max_drawdown(portfolio_values)
        self.assertIn('max_drawdown', result)
        self.assertIn('max_drawdown_duration', result)
        
    def test_trade_metrics(self):
        """Test trade metrics calculation"""
        trades = [
            {'profit': 100},
            {'profit': -50},
            {'profit': 200},
            {'profit': -30}
        ]
        metrics = self.metrics.calculate_trade_metrics(trades)
        self.assertEqual(metrics['total_trades'], 4)
        self.assertEqual(metrics['win_rate'], 0.5)

class TestLicenseManager(unittest.TestCase):
    def setUp(self):
        self.license_manager = LicenseManager()
        
    def test_hardware_id_generation(self):
        """Test hardware ID generation"""
        hw_id = self.license_manager._generate_hardware_id()
        self.assertIsInstance(hw_id, str)
        self.assertEqual(len(hw_id), 64)  # SHA256 hash length
        
    @patch.dict(os.environ, {'ADMIN_ACCESS': 'true'})
    def test_admin_access(self):
        """Test admin access bypass"""
        # Create .env file with admin access
        with open('.env', 'w') as f:
            f.write('ADMIN_ACCESS=true\n')
        
        result = self.license_manager.check_license()
        self.assertTrue(result)
        
        # Clean up
        if os.path.exists('.env'):
            os.remove('.env')

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    @patch('trading_bot.BrokerInterface')
    @patch('trading_bot.NSEDataFetcher')
    def test_bot_initialization(self, mock_data_fetcher, mock_broker):
        """Test bot initialization"""
        # Mock license check
        with patch('license_manager.LicenseManager.check_license', return_value=True):
            bot = TradingBot()
            self.assertIsNotNone(bot.strategy)
            self.assertIsNotNone(bot.risk_manager)

def run_production_tests():
    """Run all tests and return results"""
    print("=" * 60)
    print("TRADING BOT PRODUCTION TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestRiskManager,
        TestTradingStrategy,
        TestDatabase,
        TestPerformanceMetrics,
        TestLicenseManager,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOVERALL RESULT: {'PASS' if success else 'FAIL'}")
    
    return success

if __name__ == "__main__":
    success = run_production_tests()
    sys.exit(0 if success else 1)
