#!/usr/bin/env python3
"""
Production startup script for Trading Bot
Includes health checks, monitoring, and graceful shutdown
"""

import os
import sys
import time
import signal
import logging
from datetime import datetime
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import main
from monitor import monitor_bot
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/production.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ProductionManager:
    def __init__(self):
        self.running = True
        self.bot_thread = None
        self.monitor_thread = None
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
        
    def pre_flight_checks(self):
        """Perform pre-flight checks before starting"""
        logger.info("Performing pre-flight checks...")
        
        # Check environment file
        if not Path('.env').exists():
            logger.error("Environment file (.env) not found!")
            logger.info("Please copy .env.production to .env and configure it")
            return False
            
        # Check required directories
        os.makedirs('logs', exist_ok=True)
        
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("Python 3.8 or higher required")
            return False
            
        logger.info("Pre-flight checks passed")
        return True
        
    def start_bot(self):
        """Start the trading bot"""
        try:
            logger.info("Starting trading bot...")
            main()
        except Exception as e:
            logger.error(f"Bot crashed: {e}")
            
    def start_monitor(self):
        """Start the monitoring system"""
        try:
            logger.info("Starting monitoring system...")
            monitor_bot()
        except Exception as e:
            logger.error(f"Monitor crashed: {e}")
            
    def run(self):
        """Main production run method"""
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Pre-flight checks
        if not self.pre_flight_checks():
            sys.exit(1)
            
        logger.info("Starting Trading Bot in Production Mode")
        logger.info(f"Process ID: {os.getpid()}")
        logger.info(f"Start time: {datetime.now()}")
        
        try:
            # Start bot in separate thread
            self.bot_thread = threading.Thread(target=self.start_bot, daemon=True)
            self.bot_thread.start()
            
            # Start monitor in separate thread
            self.monitor_thread = threading.Thread(target=self.start_monitor, daemon=True)
            self.monitor_thread.start()
            
            # Keep main thread alive
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
        finally:
            logger.info("Shutting down...")
            
if __name__ == "__main__":
    manager = ProductionManager()
    manager.run()
