<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NSE Margin Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 20px;
            background-color: #f5f5f5;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .margin-high {
            color: #dc3545;
        }
        .margin-medium {
            color: #fd7e14;
        }
        .margin-low {
            color: #28a745;
        }
        #searchForm {
            margin-bottom: 20px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .loading-spinner {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand mb-0 h1">Trading Bot Dashboard</span>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="/margin">Margin Calculator</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="text-center mb-4">NSE Margin Requirements</h1>
        
        <div class="row">
            <div class="col-md-6 offset-md-3">
                <div class="card">
                    <div class="card-header">
                        Search Stock
                    </div>
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="input-group mb-3">
                                <input type="text" id="symbolInput" class="form-control" placeholder="Enter Stock Symbol (e.g., RELIANCE)" required>
                                <button class="btn btn-primary" type="submit">Get Margin Data</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="loading">
            <div class="spinner-border loading-spinner" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Fetching margin data...</p>
        </div>
        
        <div id="marginData"></div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Common NSE Stocks Margin Data
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Price (₹)</th>
                                        <th>SPAN Margin (₹)</th>
                                        <th>Exposure Margin (₹)</th>
                                        <th>Total Margin (₹)</th>
                                        <th>Margin %</th>
                                        <th>Lot Size</th>
                                        <th>Category</th>
                                    </tr>
                                </thead>
                                <tbody id="marginTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center">Loading common stocks margin data...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load common stocks margin data
            fetchMarginList();
            
            // Handle search form submission
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const symbol = document.getElementById('symbolInput').value.trim().toUpperCase();
                if (symbol) {
                    fetchMarginData(symbol);
                }
            });
        });
        
        function fetchMarginData(symbol) {
            // Show loading indicator
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('marginData').innerHTML = '';
            
            fetch(`/api/margin/${symbol}`)
                .then(response => response.json())
                .then(data => {
                    // Hide loading indicator
                    document.querySelector('.loading').style.display = 'none';
                    
                    if (data.status === 'ok' && data.margin_data) {
                        displayMarginData(data.margin_data);
                    } else {
                        document.getElementById('marginData').innerHTML = `
                            <div class="alert alert-danger">
                                Error: ${data.message || 'Failed to fetch margin data'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    // Hide loading indicator
                    document.querySelector('.loading').style.display = 'none';
                    
                    document.getElementById('marginData').innerHTML = `
                        <div class="alert alert-danger">
                            Error: ${error.message || 'Failed to fetch margin data'}
                        </div>
                    `;
                });
        }
        
        function displayMarginData(marginData) {
            // Determine margin class based on percentage
            let marginClass = 'margin-low';
            if (marginData.margin_percentage > 25) {
                marginClass = 'margin-high';
            } else if (marginData.margin_percentage > 15) {
                marginClass = 'margin-medium';
            }
            
            // Create HTML for margin data card
            const html = `
                <div class="row">
                    <div class="col-md-8 offset-md-2">
                        <div class="card">
                            <div class="card-header">
                                Margin Data for ${marginData.symbol}
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h5>Current Price: ₹${marginData.price.toFixed(2)}</h5>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <h5 class="${marginClass}">Margin Required: ${marginData.margin_percentage.toFixed(2)}%</h5>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tbody>
                                            <tr>
                                                <th>SPAN Margin</th>
                                                <td>₹${marginData.span_margin.toFixed(2)}</td>
                                            </tr>
                                            <tr>
                                                <th>Exposure Margin</th>
                                                <td>₹${marginData.exposure_margin.toFixed(2)}</td>
                                            </tr>
                                            <tr>
                                                <th>Total Margin</th>
                                                <td>₹${marginData.total_margin.toFixed(2)}</td>
                                            </tr>
                                            <tr>
                                                <th>Lot Size</th>
                                                <td>${marginData.lot_size}</td>
                                            </tr>
                                            ${marginData.market_cap_category ? `
                                            <tr>
                                                <th>Market Cap Category</th>
                                                <td>${marginData.market_cap_category}</td>
                                            </tr>
                                            ` : ''}
                                            <tr>
                                                <th>Last Updated</th>
                                                <td>${new Date(marginData.timestamp).toLocaleString()}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                ${marginData.is_estimate ? `
                                <div class="alert alert-warning mt-3">
                                    <strong>Note:</strong> These margin values are estimates and may vary from actual broker requirements.
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('marginData').innerHTML = html;
        }
        
        function fetchMarginList() {
            fetch('/api/margin')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'ok' && data.margin_data) {
                        displayMarginList(data.margin_data);
                    } else {
                        document.getElementById('marginTableBody').innerHTML = `
                            <tr>
                                <td colspan="8" class="text-center text-danger">
                                    Error: ${data.message || 'Failed to fetch margin data'}
                                </td>
                            </tr>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('marginTableBody').innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center text-danger">
                                Error: ${error.message || 'Failed to fetch margin data'}
                            </td>
                        </tr>
                    `;
                });
        }
        
        function displayMarginList(marginDataList) {
            if (!marginDataList || Object.keys(marginDataList).length === 0) {
                document.getElementById('marginTableBody').innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center">No margin data available</td>
                    </tr>
                `;
                return;
            }
            
            let html = '';
            
            // Sort symbols by margin percentage (highest first)
            const sortedSymbols = Object.keys(marginDataList).sort((a, b) => {
                return marginDataList[b].margin_percentage - marginDataList[a].margin_percentage;
            });
            
            for (const symbol of sortedSymbols) {
                const data = marginDataList[symbol];
                
                // Determine margin class based on percentage
                let marginClass = 'margin-low';
                if (data.margin_percentage > 25) {
                    marginClass = 'margin-high';
                } else if (data.margin_percentage > 15) {
                    marginClass = 'margin-medium';
                }
                
                html += `
                    <tr>
                        <td><a href="#" onclick="fetchMarginData('${symbol}'); return false;">${symbol}</a></td>
                        <td>₹${data.price.toFixed(2)}</td>
                        <td>₹${data.span_margin.toFixed(2)}</td>
                        <td>₹${data.exposure_margin.toFixed(2)}</td>
                        <td>₹${data.total_margin.toFixed(2)}</td>
                        <td class="${marginClass}">${data.margin_percentage.toFixed(2)}%</td>
                        <td>${data.lot_size}</td>
                        <td>${data.market_cap_category || 'N/A'}</td>
                    </tr>
                `;
            }
            
            document.getElementById('marginTableBody').innerHTML = html;
        }
    </script>
</body>
</html>