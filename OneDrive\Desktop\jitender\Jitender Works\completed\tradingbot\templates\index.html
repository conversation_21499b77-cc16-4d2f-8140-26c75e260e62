<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .card { margin-bottom: 20px; }
        .trade-row:hover { background-color: #f8f9fa; }
        .market-data { font-size: 0.9rem; }
        .profit { color: #28a745; }
        .loss { color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand mb-0 h1">Trading Bot Dashboard</span>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/margin">Margin Calculator</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Portfolio Summary -->
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Account Balance</h6>
                        <h3 id="balance">Loading...</h3>
                        <small id="balance-change" class="text-muted">Today's Change: --</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Open Positions</h6>
                        <h3 id="open-trades-count">Loading...</h3>
                        <small id="total-exposure" class="text-muted">Total Exposure: --</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Today's P/L</h6>
                        <h3 id="daily-pnl">Loading...</h3>
                        <small id="pnl-percentage" class="text-muted">Return: --</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Bot Status</h6>
                        <h3 id="bot-status">Loading...</h3>
                        <small id="last-update" class="text-muted">Last Update: --</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Chart -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Portfolio Performance</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateChart('1D')">1D</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateChart('1W')">1W</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateChart('1M')">1M</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="updateChart('3M')">3M</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="performance-chart"></canvas>
            </div>
        </div>

        <!-- Market Overview -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Top Gainers</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm market-data">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Price</th>
                                        <th>Change</th>
                                        <th>Volume</th>
                                    </tr>
                                </thead>
                                <tbody id="top-gainers"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Top Losers</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm market-data">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Price</th>
                                        <th>Change</th>
                                        <th>Volume</th>
                                    </tr>
                                </thead>
                                <tbody id="top-losers"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Open Trades -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Open Trades</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Symbol</th>
                                <th>Type</th>
                                <th>Entry Price</th>
                                <th>Current Price</th>
                                <th>Size</th>
                                <th>P/L (₹)</th>
                                <th>P/L (%)</th>
                                <th>Stop Loss</th>
                                <th>Take Profit</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="trades-table">
                            <tr><td colspan="12" class="text-center">Loading...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let performanceChart = null;

        function initializeChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Portfolio Value',
                        data: [],
                        borderColor: '#0d6efd',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: false }
                    }
                }
            });
        }

        function updateChart(timeframe) {
            fetch(`/api/performance?timeframe=${timeframe}`)
                .then(response => response.json())
                .then(data => {
                    performanceChart.data.labels = data.labels;
                    performanceChart.data.datasets[0].data = data.values;
                    performanceChart.update();
                });
        }

        function updateMarketData() {
            // Update Top Gainers
            fetch('/api/market/gainers')
                .then(response => response.json())
                .then(data => {
                    const gainersHtml = data.map(stock => `
                        <tr>
                            <td>${stock.symbol}</td>
                            <td>₹${stock.price.toFixed(2)}</td>
                            <td class="profit">+${stock.change_percent.toFixed(2)}%</td>
                            <td>${stock.volume.toLocaleString()}</td>
                        </tr>
                    `).join('');
                    document.getElementById('top-gainers').innerHTML = gainersHtml;
                });

            // Update Top Losers
            fetch('/api/market/losers')
                .then(response => response.json())
                .then(data => {
                    const losersHtml = data.map(stock => `
                        <tr>
                            <td>${stock.symbol}</td>
                            <td>₹${stock.price.toFixed(2)}</td>
                            <td class="loss">${stock.change_percent.toFixed(2)}%</td>
                            <td>${stock.volume.toLocaleString()}</td>
                        </tr>
                    `).join('');
                    document.getElementById('top-losers').innerHTML = losersHtml;
                });
        }

        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('balance').textContent = `₹${data.balance.toFixed(2)}`;
                    document.getElementById('balance-change').textContent = 
                        `Today's Change: ${data.balance_change >= 0 ? '+' : ''}${data.balance_change.toFixed(2)}%`;
                    
                    document.getElementById('open-trades-count').textContent = data.open_trades_count;
                    document.getElementById('total-exposure').textContent = 
                        `Total Exposure: ₹${data.total_exposure.toFixed(2)}`;
                    
                    document.getElementById('daily-pnl').textContent = 
                        `₹${data.daily_pnl.toFixed(2)}`;
                    document.getElementById('daily-pnl').className = 
                        data.daily_pnl >= 0 ? 'text-success' : 'text-danger';
                    document.getElementById('pnl-percentage').textContent = 
                        `Return: ${data.pnl_percentage >= 0 ? '+' : ''}${data.pnl_percentage.toFixed(2)}%`;
                    
                    document.getElementById('bot-status').textContent = data.status;
                    document.getElementById('bot-status').className = 
                        data.status === 'Running' ? 'text-success' : 'text-danger';
                    document.getElementById('last-update').textContent = 
                        `Last Update: ${new Date(data.last_update).toLocaleTimeString()}`;

                    // Update trades table
                    const tradesHtml = data.open_trades.map(trade => `
                        <tr class="trade-row">
                            <td>${trade.id}</td>
                            <td>${trade.symbol}</td>
                            <td>${trade.type}</td>
                            <td>₹${trade.entry_price.toFixed(2)}</td>
                            <td>₹${trade.current_price.toFixed(2)}</td>
                            <td>${trade.size}</td>
                            <td class="${trade.pnl >= 0 ? 'profit' : 'loss'}">₹${trade.pnl.toFixed(2)}</td>
                            <td class="${trade.pnl_percent >= 0 ? 'profit' : 'loss'}">${trade.pnl_percent.toFixed(2)}%</td>
                            <td>₹${trade.stop_loss.toFixed(2)}</td>
                            <td>₹${trade.take_profit.toFixed(2)}</td>
                            <td>${trade.duration}</td>
                            <td>${trade.status}</td>
                        </tr>
                    `).join('');
                    document.getElementById('trades-table').innerHTML = 
                        data.open_trades.length ? tradesHtml : '<tr><td colspan="12" class="text-center">No open trades</td></tr>';
                });
        }

        // Initialize chart and start updates
        document.addEventListener('DOMContentLoaded', () => {
            initializeChart();
            updateChart('1D');
            updateStatus();
            updateMarketData();
            
            // Refresh data every 5 seconds
            setInterval(updateStatus, 5000);
            setInterval(updateMarketData, 5000);
        });
    </script>
</body>
</html>