"""
Authentication module for web interface
"""

import hashlib
import secrets
from functools import wraps
from flask import session, request, jsonify, redirect, url_for
import os
from datetime import datetime, timedelta

class AuthManager:
    def __init__(self):
        self.users = {
            'admin': self._hash_password(os.getenv('ADMIN_PASSWORD', 'admin123')),
            'trader': self._hash_password(os.getenv('TRADER_PASSWORD', 'trader123'))
        }
        self.sessions = {}
        
    def _hash_password(self, password):
        """Hash password with salt"""
        salt = secrets.token_hex(16)
        pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{pwd_hash.hex()}"
        
    def _verify_password(self, password, hashed):
        """Verify password against hash"""
        try:
            salt, pwd_hash = hashed.split(':')
            return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex() == pwd_hash
        except:
            return False
            
    def authenticate(self, username, password):
        """Authenticate user"""
        if username in self.users:
            if self._verify_password(password, self.users[username]):
                session_token = secrets.token_urlsafe(32)
                self.sessions[session_token] = {
                    'username': username,
                    'created': datetime.now(),
                    'last_activity': datetime.now()
                }
                return session_token
        return None
        
    def is_authenticated(self, session_token):
        """Check if session is valid"""
        if session_token in self.sessions:
            session_data = self.sessions[session_token]
            # Check if session is expired (24 hours)
            if datetime.now() - session_data['created'] < timedelta(hours=24):
                # Update last activity
                session_data['last_activity'] = datetime.now()
                return True
            else:
                # Remove expired session
                del self.sessions[session_token]
        return False
        
    def logout(self, session_token):
        """Logout user"""
        if session_token in self.sessions:
            del self.sessions[session_token]
            
    def require_auth(self, f):
        """Decorator to require authentication"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            session_token = session.get('auth_token') or request.headers.get('Authorization')
            
            if not session_token or not self.is_authenticated(session_token):
                if request.is_json:
                    return jsonify({'error': 'Authentication required'}), 401
                else:
                    return redirect(url_for('login'))
                    
            return f(*args, **kwargs)
        return decorated_function
        
    def get_user_role(self, session_token):
        """Get user role from session"""
        if session_token in self.sessions:
            username = self.sessions[session_token]['username']
            if username == 'admin':
                return 'admin'
            else:
                return 'trader'
        return None

# Global auth manager instance
auth_manager = AuthManager()
